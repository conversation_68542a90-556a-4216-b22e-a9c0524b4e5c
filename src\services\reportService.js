const path = require('path');
const { writeTXT, generateTimestamp } = require('../utils/fileUtils');

/**
 * Service class for generating reports and output files
 */
class ReportService {
  
  /**
   * Generates all output files and reports
   * @param {Object} matchingResults - Results from price matching
   * @param {Object} priceResults - Results from price processing
   * @param {Object} fileInfo - Information about uploaded files
   * @returns {Object} File paths and statistics
   */
  static generateReports(matchingResults, priceResults, fileInfo) {
    const timestamp = generateTimestamp();
    
    const {
      completeExportData,
      matchCount,
      emptyVendprodCount,
      itemsNotInExport
    } = matchingResults;

    const {
      validPriceItems
    } = priceResults;

    const { exportData } = fileInfo;
    
    // Generate file paths
    const filePaths = this.generateFilePaths(timestamp);
    
    // Write main output files - now using complete export data instead of just matched items
    writeTXT(filePaths.matchedPath, completeExportData);

    // Write additional report files
    if (itemsNotInExport.length > 0) {
      writeTXT(filePaths.notInExportPath, itemsNotInExport);
    }
    

    // Log file creation
    this.logFileCreation(filePaths, itemsNotInExport);

    return {
      filePaths,
      stats: this.calculateStats(exportData, matchCount, validPriceItems,
                                 itemsNotInExport, emptyVendprodCount)
    };
  }
  
  /**
   * Generates file paths for all output files
   * @param {string} timestamp - Timestamp for file naming
   * @returns {Object} Object containing all file paths
   */
  static generateFilePaths(timestamp) {
    const publicDir = path.join(process.cwd(), 'public');
    return {
      matchedPath: path.join(publicDir, `UNITED_ABRASIVES_ICSW_Price_Updated_${timestamp}.txt`),
      notInExportPath: path.join(publicDir, `Items_in_Vendor_PriceFile_not_in_Infor_${timestamp}.txt`)
    };
  }
  

  
  /**
   * Logs file creation to console
   * @param {Object} filePaths - Object containing file paths
   * @param {Array} itemsNotInExport - Items not in export
   */
  static logFileCreation(filePaths, itemsNotInExport) {
    console.log(`Written complete export file with updated prices to ${filePaths.matchedPath}`);

    if (itemsNotInExport.length > 0) {
      console.log(`Written items not in export to ${filePaths.notInExportPath}`);
    }
  }
  
  /**
   * Calculates statistics for the response
   * @param {Array} exportData - Export data
   * @param {number} matchCount - Number of matched items
   * @param {number} validPriceItems - Number of valid price items
   * @param {Array} itemsNotInExport - Items not in export
   * @param {number} emptyVendprodCount - Count of empty vendor product codes
   * @returns {Object} Statistics object
   */
  static calculateStats(exportData, matchCount, validPriceItems,
                       itemsNotInExport, emptyVendprodCount) {
    return {
      totalItems: exportData.length,
      matchedItems: matchCount,
      priceItems: validPriceItems,
      itemsNotInExport: itemsNotInExport.length,
      emptyVendprodCount: emptyVendprodCount
    };
  }
  
  /**
   * Gets file names without paths for response
   * @param {Object} filePaths - Object containing file paths
   * @param {Array} itemsNotInExport - Items not in export
   * @returns {Object} File names for response
   */
  static getResponseFileNames(filePaths, itemsNotInExport) {
    return {
      matchedFile: path.basename(filePaths.matchedPath),
      notInExportFile: itemsNotInExport.length > 0 ? path.basename(filePaths.notInExportPath) : null
    };
  }
}

module.exports = ReportService;