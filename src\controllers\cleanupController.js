const CleanupService = require('../services/cleanupService');

/**
 * Controller for handling cleanup operations
 */
class CleanupController {
  
  /**
   * Gets list of generated files and cleanup statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getCleanupInfo(req, res) {
    try {
      const files = CleanupService.getGeneratedFiles();
      const stats = CleanupService.getCleanupStats();
      
      // Format file information for response
      const formattedFiles = files.map(file => ({
        name: file.name,
        type: file.type,
        size: CleanupService.formatFileSize(file.size),
        sizeBytes: file.size,
        created: file.created.toISOString(),
        createdFormatted: file.created.toLocaleString(),
        modified: file.modified.toISOString(),
        modifiedFormatted: file.modified.toLocaleString()
      }));
      
      res.json({
        success: true,
        files: formattedFiles,
        stats: {
          ...stats,
          totalSizeFormatted: CleanupService.formatFileSize(stats.totalSize)
        }
      });
      
    } catch (error) {
      console.error('Error getting cleanup info:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get cleanup information'
      });
    }
  }
  
  /**
   * Cleans up files older than specified days
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async cleanupOldFiles(req, res) {
    try {
      const { days } = req.body;
      const daysToClean = parseInt(days) || 7;
      
      // Validate days parameter
      if (daysToClean < 1 || daysToClean > 365) {
        return res.status(400).json({
          success: false,
          error: 'Days must be between 1 and 365'
        });
      }
      
      const result = CleanupService.cleanupOldFiles(daysToClean);
      
      res.json(result);
      
    } catch (error) {
      console.error('Error cleaning up old files:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to cleanup old files'
      });
    }
  }
  
  /**
   * Cleans up all generated files
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async cleanupAllFiles(req, res) {
    try {
      const result = CleanupService.cleanupAllFiles();
      
      res.json(result);
      
    } catch (error) {
      console.error('Error cleaning up all files:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to cleanup all files'
      });
    }
  }
  
  /**
   * Deletes specific files
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteSpecificFiles(req, res) {
    try {
      const { fileNames } = req.body;
      
      if (!Array.isArray(fileNames) || fileNames.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'fileNames must be a non-empty array'
        });
      }
      
      const result = CleanupService.deleteSpecificFiles(fileNames);
      
      res.json(result);
      
    } catch (error) {
      console.error('Error deleting specific files:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete specific files'
      });
    }
  }
  
  /**
   * Gets cleanup statistics only
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getCleanupStats(req, res) {
    try {
      const stats = CleanupService.getCleanupStats();
      
      res.json({
        success: true,
        stats: {
          ...stats,
          totalSizeFormatted: CleanupService.formatFileSize(stats.totalSize)
        }
      });
      
    } catch (error) {
      console.error('Error getting cleanup stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get cleanup statistics'
      });
    }
  }
}

module.exports = CleanupController;
