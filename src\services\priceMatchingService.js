const xlsx = require('xlsx');

/**
 * Service class for handling price matching operations
 */
class PriceMatchingService {
  
  /**
   * Processes Excel price file and extracts price data
   * @param {string} priceFilePath - Path to the Excel file
   * @returns {Object} Object containing price map and statistics
   */
  static processPriceFile(priceFilePath) {
    console.log(`Processing price file: ${priceFilePath}`);
    
    const wb = xlsx.readFile(priceFilePath);
    
    if (!wb.SheetNames || wb.SheetNames.length === 0) {
      throw new Error('Excel file contains no sheets');
    }
    
    const ws = wb.Sheets[wb.SheetNames[0]];
    
    // Find header row
    const range = this.findHeaderRow(ws);
    console.log(`Skipping ${range} rows in Excel file`);
    
    const priceData = xlsx.utils.sheet_to_json(ws, { range: range });
    console.log(`Found ${priceData.length} items in price file`);

    return this.buildPriceMap(priceData);
  }

  /**
   * Finds the header row in the Excel sheet
   * @param {Object} ws - Excel worksheet object
   * @returns {number} Row number where headers are found
   */
  static findHeaderRow(ws) {
    const sampleData = xlsx.utils.sheet_to_json(ws, { header: 1, range: 0 });
    
    // Look for the row containing "Item #" and "BASE PRICE" headers
    for (let i = 0; i < Math.min(20, sampleData.length); i++) {
      if (sampleData[i] && 
          sampleData[i].some(cell => cell === 'Item #') && 
          sampleData[i].some(cell => cell === 'BASE PRICE')) {
        return i;
      }
    }
    
    // If we didn't find headers, default to skipping 10 rows
    return sampleData.length > 10 ? 10 : 0;
  }

  /**
   * Builds a price map from Excel data
   * @param {Array} priceData - Raw price data from Excel
   * @returns {Object} Object containing price map and invalid items
   */
  static buildPriceMap(priceData) {
    const priceMap = {};
    let validPriceItems = 0;
    const invalidPriceItems = [];
    
    for (const item of priceData) {
      // Check for both column name variations
      const itemNumber = item['Item #'] || item['Item#'] || item['ItemNo'] || item['Item No'];
      const basePrice = item['BASE PRICE'] || item['Base Price'] || item['Price'] || item['PRICE'];
      
      if (itemNumber) {
        const key = itemNumber.toString().trim();
        
        if (basePrice !== undefined) {
          const price = parseFloat(basePrice);
          
          if (!isNaN(price)) {
            priceMap[key] = price;
            validPriceItems++;
          } else {
            invalidPriceItems.push({
              itemNumber: key,
              price: basePrice,
              reason: 'Invalid price format (not a number)'
            });
          }
        } else {
          invalidPriceItems.push({
            itemNumber: key,
            reason: 'Missing price value'
          });
        }
      } else {
        invalidPriceItems.push({
          row: item,
          reason: 'Missing item number'
        });
      }
    }
    
    console.log(`Found ${validPriceItems} valid price items out of ${priceData.length} total items`);
    
    return {
      priceMap,
      validPriceItems,
      invalidPriceItems,
      priceData
    };
  }

  /**
   * Matches export data with price data
   * @param {Array} exportData - Export data from TSV file
   * @param {Object} priceMap - Price mapping object
   * @param {Array} priceData - Original price data for reference
   * @returns {Object} Matching results and statistics
   */
  static matchPrices(exportData, priceMap, priceData) {
    const completeExportData = [];
    const unmatchedItems = [];
    let matchCount = 0;
    let emptyVendprodCount = 0;

    // Create normalized price map for fuzzy matching
    const { normalizedPriceMap, normalizedToOriginalMap } = this.createNormalizedPriceMap(priceMap);

    // Process each export item
    for (const row of exportData) {
      // Create a copy of the row to avoid modifying the original
      const processedRow = { ...row };
      
      if (!processedRow.vendprod) {
        // Add to unmatched items without tracking columns
        unmatchedItems.push({ ...processedRow });
        emptyVendprodCount++;
      } else {
        const key = processedRow.vendprod.trim();
        
        // Try exact match first
        if (priceMap.hasOwnProperty(key)) {
          processedRow.baseprice = priceMap[key];
          matchCount++;
        } 
        // Try normalized match if exact match fails
        else {
          const normalizedKey = key.toLowerCase().replace(/[^a-z0-9]/g, '');
          if (normalizedPriceMap.hasOwnProperty(normalizedKey)) {
            processedRow.baseprice = normalizedPriceMap[normalizedKey];
            matchCount++;
          } else {
            // Add to unmatched items without tracking columns
            unmatchedItems.push({ ...processedRow });
          }
        }
      }
      
      // Add all items to complete export data (clean, without tracking columns)
      completeExportData.push(processedRow);
    }

    // Calculate additional statistics
    const itemsNotInExport = this.findItemsNotInExport(priceMap, exportData, priceData);

    return {
      completeExportData,
      unmatchedItems,
      matchCount,
      emptyVendprodCount,
      itemsNotInExport
    };
  }

  /**
   * Creates normalized version of price map for fuzzy matching
   * @param {Object} priceMap - Original price map
   * @returns {Object} Normalized maps
   */
  static createNormalizedPriceMap(priceMap) {
    const normalizedPriceMap = {};
    const normalizedToOriginalMap = {};
    
    for (const key in priceMap) {
      const normalizedKey = key.toLowerCase().replace(/[^a-z0-9]/g, '');
      normalizedPriceMap[normalizedKey] = priceMap[key];
      normalizedToOriginalMap[normalizedKey] = key;
    }
    
    return { normalizedPriceMap, normalizedToOriginalMap };
  }

  /**
   * Finds items in price file that don't exist in export
   * @param {Object} priceMap - Price mapping object
   * @param {Array} exportData - Export data
   * @param {Array} priceData - Original price data
   * @returns {Array} Array of items not in export
   */
  static findItemsNotInExport(priceMap, exportData, priceData) {
    const exportProductCodes = new Set();
    exportData.forEach(row => {
      if (row.vendprod) {
        exportProductCodes.add(row.vendprod.trim());
        exportProductCodes.add(row.vendprod.trim().toLowerCase().replace(/[^a-z0-9]/g, ''));
      }
    });
    
    const priceDataMap = this.createPriceDataMap(priceData);
    const itemsNotInExport = [];
    
    for (const key in priceMap) {
      const normalizedKey = key.toLowerCase().replace(/[^a-z0-9]/g, '');
      if (!exportProductCodes.has(key) && !exportProductCodes.has(normalizedKey)) {
        const originalItem = priceDataMap[key] || {};
        
        itemsNotInExport.push({
          itemNumber: key,          
          description: originalItem['Description'] || originalItem['DESCRIPTION'] || originalItem['Item Description'] || '',
          baseprice: priceMap[key],
        });
      }
    }
    
    return itemsNotInExport;
  }

  /**
   * Creates a map of price data for quick lookup
   * @param {Array} priceData - Original price data
   * @returns {Object} Price data map
   */
  static createPriceDataMap(priceData) {
    const priceDataMap = {};
    priceData.forEach(item => {
      const itemNumber = item['Item #'] || item['Item#'] || item['ItemNo'] || item['Item No'];
      if (itemNumber) {
        priceDataMap[itemNumber.toString().trim()] = item;
      }
    });
    return priceDataMap;
  }
}

module.exports = PriceMatchingService;