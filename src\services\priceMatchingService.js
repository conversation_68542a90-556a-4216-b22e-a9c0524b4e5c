const xlsx = require('xlsx');

/**
 * Service class for handling price matching operations
 */
class PriceMatchingService {
  
  /**
   * Processes Excel price file and extracts price data
   * @param {string} priceFilePath - Path to the Excel file
   * @returns {Object} Object containing price map and statistics
   */
  static processPriceFile(priceFilePath) {
    console.log(`Processing price file: ${priceFilePath}`);
    
    const wb = xlsx.readFile(priceFilePath);
    
    if (!wb.SheetNames || wb.SheetNames.length === 0) {
      throw new Error('Excel file contains no sheets');
    }
    
    const ws = wb.Sheets[wb.SheetNames[0]];
    
    // Find header row
    const range = this.findHeaderRow(ws);
    console.log(`Skipping ${range} rows in Excel file`);
    
    const priceData = xlsx.utils.sheet_to_json(ws, { range: range });
    console.log(`Found ${priceData.length} items in price file`);

    return this.buildPriceMap(priceData);
  }

  /**
   * Finds the header row in the Excel sheet
   * @param {Object} ws - Excel worksheet object
   * @returns {number} Row number where headers are found
   */
  static findHeaderRow(ws) {
    const sampleData = xlsx.utils.sheet_to_json(ws, { header: 1, range: 0 });

    // Look for the row containing "Item #" and "BASE PRICE" headers
    for (let i = 0; i < Math.min(20, sampleData.length); i++) {
      if (sampleData[i] &&
          sampleData[i].some(cell => cell === 'Item #') &&
          sampleData[i].some(cell => cell === 'BASE PRICE')) {
        return i;
      }
    }

    // If we didn't find headers, default to skipping 10 rows
    return sampleData.length > 10 ? 10 : 0;
  }

  /**
   * Builds a price map from Excel data
   * @param {Array} priceData - Raw price data from Excel
   * @returns {Object} Object containing price maps and invalid items
   */
  static buildPriceMap(priceData) {
    const basePriceMap = {};
    const listPriceMap = {};
    let validPriceItems = 0;
    const invalidPriceItems = [];

    for (const item of priceData) {
      // Check for both column name variations
      const itemNumber = item['Item #'] || item['Item#'] || item['ItemNo'] || item['Item No'];
      const basePrice = item['BASE PRICE'] || item['Base Price'] || item['Price'] || item['PRICE'];
      const listPrice = item['LIST PRICE'] || item['List Price'] || item['LISTPRICE'] || item['ListPrice'];

      if (itemNumber) {
        const key = itemNumber.toString().trim();
        let hasValidPrice = false;

        // Process base price
        if (basePrice !== undefined) {
          const parsedBasePrice = parseFloat(basePrice);

          if (!isNaN(parsedBasePrice)) {
            basePriceMap[key] = parsedBasePrice;
            hasValidPrice = true;
          } else {
            invalidPriceItems.push({
              itemNumber: key,
              price: basePrice,
              reason: 'Invalid base price format (not a number)'
            });
          }
        }

        // Process list price
        if (listPrice !== undefined) {
          const parsedListPrice = parseFloat(listPrice);

          if (!isNaN(parsedListPrice)) {
            listPriceMap[key] = parsedListPrice;
            hasValidPrice = true;
          } else {
            invalidPriceItems.push({
              itemNumber: key,
              price: listPrice,
              reason: 'Invalid list price format (not a number)'
            });
          }
        }

        if (hasValidPrice) {
          validPriceItems++;
        } else if (basePrice === undefined && listPrice === undefined) {
          invalidPriceItems.push({
            itemNumber: key,
            reason: 'Missing both base price and list price values'
          });
        }
      } else {
        invalidPriceItems.push({
          row: item,
          reason: 'Missing item number'
        });
      }
    }

    console.log(`Found ${validPriceItems} valid price items out of ${priceData.length} total items`);
    console.log(`Base prices found: ${Object.keys(basePriceMap).length}, List prices found: ${Object.keys(listPriceMap).length}`);

    return {
      basePriceMap,
      listPriceMap,
      // Keep legacy priceMap for backward compatibility
      priceMap: basePriceMap,
      validPriceItems,
      invalidPriceItems,
      priceData
    };
  }

  /**
   * Matches export data with price data
   * @param {Array} exportData - Export data from TSV file
   * @param {Object} priceResults - Results from buildPriceMap containing both price maps
   * @param {Array} priceData - Original price data for reference
   * @returns {Object} Matching results and statistics
   */
  static matchPrices(exportData, priceResults, priceData) {
    const { basePriceMap, listPriceMap, priceMap } = priceResults;
    const completeExportData = [];
    const unmatchedItems = [];
    let matchCount = 0;
    let emptyVendprodCount = 0;

    // Create normalized price maps for fuzzy matching
    const { normalizedPriceMap: normalizedBasePriceMap } = this.createNormalizedPriceMap(basePriceMap);
    const { normalizedPriceMap: normalizedListPriceMap } = this.createNormalizedPriceMap(listPriceMap);

    // Process each export item
    for (const row of exportData) {
      // Create a copy of the row to avoid modifying the original
      const processedRow = { ...row };

      if (!processedRow.vendprod) {
        // Add to unmatched items without tracking columns
        unmatchedItems.push({ ...processedRow });
        emptyVendprodCount++;
      } else {
        const key = processedRow.vendprod.trim();
        let hasMatch = false;

        // Try exact match first for base price
        if (basePriceMap.hasOwnProperty(key)) {
          processedRow.baseprice = basePriceMap[key];
          hasMatch = true;
        }
        // Try normalized match for base price if exact match fails
        else {
          const normalizedKey = key.toLowerCase().replace(/[^a-z0-9]/g, '');
          if (normalizedBasePriceMap.hasOwnProperty(normalizedKey)) {
            processedRow.baseprice = normalizedBasePriceMap[normalizedKey];
            hasMatch = true;
          }
        }

        // Try exact match for list price
        if (listPriceMap.hasOwnProperty(key)) {
          processedRow.listprice = listPriceMap[key];
          hasMatch = true;
        }
        // Try normalized match for list price if exact match fails
        else {
          const normalizedKey = key.toLowerCase().replace(/[^a-z0-9]/g, '');
          if (normalizedListPriceMap.hasOwnProperty(normalizedKey)) {
            processedRow.listprice = normalizedListPriceMap[normalizedKey];
            hasMatch = true;
          }
        }

        if (hasMatch) {
          // Update standard cost date with current date when any price match is found
          const currentDate = new Date().toLocaleDateString('en-US', { year: '2-digit', month: '2-digit', day: '2-digit' }); // Format: mm/dd/yy
          processedRow.stndcostdt = currentDate;
          matchCount++;
        } else {
          // Add to unmatched items without tracking columns
          unmatchedItems.push({ ...processedRow });
        }
      }

      // Add all items to complete export data (clean, without tracking columns)
      completeExportData.push(processedRow);
    }

    // Calculate additional statistics
    const itemsNotInExport = this.findItemsNotInExport(priceMap, exportData, priceData);

    return {
      completeExportData,
      unmatchedItems,
      matchCount,
      emptyVendprodCount,
      itemsNotInExport
    };
  }

  /**
   * Creates normalized version of price map for fuzzy matching
   * @param {Object} priceMap - Original price map
   * @returns {Object} Normalized maps
   */
  static createNormalizedPriceMap(priceMap) {
    const normalizedPriceMap = {};
    const normalizedToOriginalMap = {};
    
    for (const key in priceMap) {
      const normalizedKey = key.toLowerCase().replace(/[^a-z0-9]/g, '');
      normalizedPriceMap[normalizedKey] = priceMap[key];
      normalizedToOriginalMap[normalizedKey] = key;
    }
    
    return { normalizedPriceMap, normalizedToOriginalMap };
  }

  /**
   * Finds items in price file that don't exist in export
   * @param {Object} priceMap - Price mapping object
   * @param {Array} exportData - Export data
   * @param {Array} priceData - Original price data
   * @returns {Array} Array of items not in export
   */
  static findItemsNotInExport(priceMap, exportData, priceData) {
    const exportProductCodes = new Set();
    exportData.forEach(row => {
      if (row.vendprod) {
        exportProductCodes.add(row.vendprod.trim());
        exportProductCodes.add(row.vendprod.trim().toLowerCase().replace(/[^a-z0-9]/g, ''));
      }
    });
    
    const priceDataMap = this.createPriceDataMap(priceData);
    const itemsNotInExport = [];
    
    for (const key in priceMap) {
      const normalizedKey = key.toLowerCase().replace(/[^a-z0-9]/g, '');
      if (!exportProductCodes.has(key) && !exportProductCodes.has(normalizedKey)) {
        const originalItem = priceDataMap[key] || {};
        
        itemsNotInExport.push({
          itemNumber: key,
          description: originalItem['Description'] || originalItem['DESCRIPTION'] || originalItem['Item Description'] || '',
          baseprice: priceMap[key],
        });
      }
    }
    
    return itemsNotInExport;
  }

  /**
   * Creates a map of price data for quick lookup
   * @param {Array} priceData - Original price data
   * @returns {Object} Price data map
   */
  static createPriceDataMap(priceData) {
    const priceDataMap = {};
    priceData.forEach(item => {
      const itemNumber = item['Item #'] || item['Item#'] || item['ItemNo'] || item['Item No'];
      if (itemNumber) {
        priceDataMap[itemNumber.toString().trim()] = item;
      }
    });
    return priceDataMap;
  }
}

module.exports = PriceMatchingService;