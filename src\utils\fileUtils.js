const fs = require('fs');
const path = require('path');

/**
 * Parses a TSV (Tab-Separated Values) file
 * @param {string} filePath - Path to the TSV file
 * @returns {Array} Array of objects representing the parsed data
 */
function parseTSV(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    const lines = data.trim().split('\n');
    
    if (lines.length === 0) {
      throw new Error('Export file is empty');
    }
    
    const headers = lines[0].split('\t').map(h => h.trim());
    
    if (!headers.includes('vendprod')) {
      throw new Error('Export file missing required "vendprod" column');
    }
    
    return lines.slice(1).map((line, index) => {
      const values = line.split('\t');
      const obj = {};
      
      headers.forEach((key, i) => {
        obj[key] = values[i] ? values[i].trim() : '';
      });
      
      // Validate required fields
      if (!obj.vendprod) {
        console.warn(`Warning: Row ${index + 2} is missing vendprod value`);
      }
      
      return obj;
    });
  } catch (err) {
    if (err.code === 'ENOENT') {
      throw new Error(`File not found: ${filePath}`);
    }
    throw err;
  }
}

/**
 * Writes data to a TXT file in tab-separated format
 * @param {string} filePath - Path where to write the file
 * @param {Array} data - Array of objects to write
 */
function writeTXT(filePath, data) {
  if (!data || data.length === 0) {
    // Create empty file with headers if no data
    fs.writeFileSync(filePath, '');
    return;
  }
  
  const headers = Object.keys(data[0]);
  const lines = [headers.join('\t')];
  
  for (const row of data) {
    lines.push(headers.map(h => row[h] !== undefined ? row[h] : '').join('\t'));
  }
  
  // Ensure directory exists
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  fs.writeFileSync(filePath, lines.join('\n'));
}

/**
 * Generates a timestamp string for file naming
 * @returns {string} Formatted timestamp
 */
function generateTimestamp() {
  return new Date().toISOString().replace(/[:.]/g, '-');
}

module.exports = {
  parseTSV,
  writeTXT,
  generateTimestamp
};