const fs = require('fs');

/**
 * Safely deletes a file if it exists
 * @param {string} filePath - Path to the file to delete
 */
function safeDeleteFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`Deleted file: ${filePath}`);
    }
  } catch (err) {
    console.error(`Error deleting file ${filePath}:`, err);
  }
}

module.exports = {
  safeDeleteFile
};